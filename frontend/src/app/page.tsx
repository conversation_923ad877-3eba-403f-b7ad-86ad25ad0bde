'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Check if user is already logged in
    const token = document.cookie.split(';').find(cookie => cookie.trim().startsWith('token='));
    if (token) {
      router.push('/dashboard');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg width="60" height="60" viewBox="0 0 60 60" className="absolute inset-0 h-full w-full">
          <defs>
            <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
              <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#1e40af" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      {/* Hero Section */}
      <div className="relative overflow-hidden z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                {/* Left side - Text content */}
                <div className="text-left">
                  <h1 className="text-4xl tracking-tight font-bold text-gray-900 sm:text-5xl md:text-6xl">
                    <span className="block text-blue-600">Optimizing</span>
                    <span className="block">Maritime's Last Mile</span>
                  </h1>
                  <p className="mt-6 text-base text-gray-600 sm:text-lg sm:max-w-xl md:mt-8 md:text-xl">
                    AI-powered, End-to-End Port Cost Management Software Solution
                  </p>
                  <p className="mt-4 text-sm text-gray-500 sm:text-base">
                    Seamless Disbursement Account Analysis Across Every Port Call.
                    Our comprehensive platform streamlines maritime operations, reduces costs,
                    and provides accurate calculations for all your port service needs including
                    pilotage, tugboat services, port dues, and agency fees.
                  </p>

                  {/* CTA Buttons */}
                  <div className="mt-8 sm:mt-10 sm:flex sm:justify-start">
                    <div className="rounded-md shadow">
                      <Link
                        href="/register"
                        className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10 transition-all duration-200"
                      >
                        Get Started
                      </Link>
                    </div>
                    <div className="mt-3 sm:mt-0 sm:ml-3">
                      <Link
                        href="/login"
                        className="w-full flex items-center justify-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10 transition-all duration-200"
                      >
                        Sign In
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Right side - Lighthouse with animation */}
                <div className="relative flex justify-center lg:justify-end">
                  <div className="relative">
                    {/* Background elements */}
                    <div className="absolute inset-0 bg-gradient-to-b from-blue-100 to-blue-200 rounded-full opacity-20 blur-3xl"></div>

                    {/* Lighthouse SVG */}
                    <div className="lighthouse-container relative z-10">
                      <svg
                        width="300"
                        height="400"
                        viewBox="0 0 300 400"
                        className="lighthouse"
                      >
                        {/* Gradient definitions */}
                        <defs>
                          <linearGradient id="lightGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#FFF700" stopOpacity="0.9" />
                            <stop offset="30%" stopColor="#FFD700" stopOpacity="0.6" />
                            <stop offset="70%" stopColor="#FFA500" stopOpacity="0.3" />
                            <stop offset="100%" stopColor="#FFF700" stopOpacity="0.1" />
                          </linearGradient>
                          <linearGradient id="towerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#F0F0F0" />
                            <stop offset="50%" stopColor="#FFFFFF" />
                            <stop offset="100%" stopColor="#E0E0E0" />
                          </linearGradient>
                          <radialGradient id="lightSource" cx="50%" cy="50%" r="50%">
                            <stop offset="0%" stopColor="#FFFF00" stopOpacity="1" />
                            <stop offset="70%" stopColor="#FFD700" stopOpacity="0.8" />
                            <stop offset="100%" stopColor="#FFA500" stopOpacity="0.3" />
                          </radialGradient>
                        </defs>

                        {/* Water waves - animated */}
                        <g className="water-waves">
                          <path d="M 0 380 Q 75 370 150 380 T 300 380 L 300 400 L 0 400 Z" fill="#4299E1" opacity="0.6" />
                          <path d="M 0 390 Q 75 385 150 390 T 300 390 L 300 400 L 0 400 Z" fill="#3182CE" opacity="0.4" />
                          <path d="M 0 385 Q 50 375 100 385 T 200 385 T 300 385 L 300 400 L 0 400 Z" fill="#2B6CB0" opacity="0.3" />
                        </g>

                        {/* Lighthouse base */}
                        <rect x="120" y="350" width="60" height="50" fill="#8B4513" stroke="#654321" strokeWidth="1" />
                        <rect x="115" y="345" width="70" height="10" fill="#A0522D" />

                        {/* Lighthouse tower with gradient */}
                        <polygon points="130,350 170,350 160,100 140,100" fill="url(#towerGradient)" stroke="#D0D0D0" strokeWidth="1" />

                        {/* Lighthouse stripes */}
                        <rect x="130" y="150" width="40" height="20" fill="#DC2626" />
                        <rect x="130" y="200" width="40" height="20" fill="#DC2626" />
                        <rect x="130" y="250" width="40" height="20" fill="#DC2626" />
                        <rect x="130" y="300" width="40" height="20" fill="#DC2626" />

                        {/* Lighthouse top structure */}
                        <rect x="125" y="80" width="50" height="30" fill="#4A5568" stroke="#2D3748" strokeWidth="1" />
                        <polygon points="125,80 175,80 160,60 140,60" fill="#2D3748" stroke="#1A202C" strokeWidth="1" />

                        {/* Light source */}
                        <circle cx="150" cy="95" r="8" fill="url(#lightSource)" className="light-source" />

                        {/* Light beam - animated */}
                        <g className="light-beam">
                          <path
                            d="M 150 90 L 280 50 L 290 70 L 285 90 L 290 110 L 280 130 L 150 100 Z"
                            fill="url(#lightGradient)"
                            opacity="0.8"
                          />
                          <path
                            d="M 150 95 L 270 65 L 275 85 L 270 105 L 150 100 Z"
                            fill="#FFFF00"
                            opacity="0.4"
                          />
                        </g>

                        {/* Additional atmospheric effects */}
                        <circle cx="80" cy="120" r="15" fill="#FFFFFF" opacity="0.6" className="cloud cloud-1" />
                        <circle cx="90" cy="115" r="12" fill="#FFFFFF" opacity="0.5" className="cloud cloud-1" />
                        <circle cx="220" cy="140" r="18" fill="#FFFFFF" opacity="0.4" className="cloud cloud-2" />
                        <circle cx="235" cy="135" r="14" fill="#FFFFFF" opacity="0.3" className="cloud cloud-2" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Comprehensive Maritime Solutions
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Everything you need to manage your maritime operations efficiently
            </p>
          </div>

          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {/* Feature 1 */}
              <div className="relative group">
                <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-center justify-center h-16 w-16 rounded-lg bg-blue-500 text-white mx-auto mb-6">
                    <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 text-center mb-4">
                    Maritime Calculations
                  </h3>
                  <p className="text-gray-600 text-center leading-relaxed">
                    Accurate proforma calculations for all maritime services including pilotage,
                    tugboat, and port dues with real-time tariff updates.
                  </p>
                </div>
              </div>

              {/* Feature 2 */}
              <div className="relative group">
                <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-center justify-center h-16 w-16 rounded-lg bg-green-500 text-white mx-auto mb-6">
                    <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 text-center mb-4">
                    Cost Management
                  </h3>
                  <p className="text-gray-600 text-center leading-relaxed">
                    Track and manage all your maritime costs with detailed breakdowns,
                    historical data, and predictive analytics.
                  </p>
                </div>
              </div>

              {/* Feature 3 */}
              <div className="relative group">
                <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-center justify-center h-16 w-16 rounded-lg bg-purple-500 text-white mx-auto mb-6">
                    <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 text-center mb-4">
                    Professional Reports
                  </h3>
                  <p className="text-gray-600 text-center leading-relaxed">
                    Generate professional proforma reports and documentation
                    for your clients and stakeholders with PDF export.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600">
            Professional maritime calculation software for shipping industry professionals.
          </p>
          <p className="mt-2 text-sm text-gray-500">
            Streamline your operations with our comprehensive port cost management solution.
          </p>
        </div>
      </div>
    </div>
  );
}
