@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Header specific styles */
.header-dropdown {
  transition: all 0.2s ease-in-out;
}

/* Form styles */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
  @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* Button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors;
}

/* Prose styles for content pages */
.prose {
  @apply text-gray-700;
}

.prose h2 {
  @apply text-2xl font-bold text-gray-900 mt-8 mb-4;
}

.prose h3 {
  @apply text-xl font-semibold text-gray-900 mt-6 mb-3;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul {
  @apply list-disc list-inside mb-4 space-y-2;
}

.prose li {
  @apply text-gray-700;
}

/* Lighthouse Animation Styles */
.lighthouse-container {
  position: relative;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.light-beam {
  animation: lighthouse-sweep 5s linear infinite;
  transform-origin: 150px 95px;
}

@keyframes lighthouse-sweep {
  0% {
    transform: rotate(-45deg);
    opacity: 0.2;
  }
  15% {
    opacity: 0.6;
  }
  25% {
    transform: rotate(-15deg);
    opacity: 0.9;
  }
  35% {
    opacity: 0.9;
  }
  50% {
    transform: rotate(15deg);
    opacity: 0.9;
  }
  65% {
    opacity: 0.6;
  }
  75% {
    transform: rotate(45deg);
    opacity: 0.2;
  }
  100% {
    transform: rotate(-45deg);
    opacity: 0.2;
  }
}

/* Light source pulsing */
.light-source {
  animation: light-pulse 2s ease-in-out infinite;
}

@keyframes light-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Water wave animation */
.water-waves {
  animation: wave-motion 3s ease-in-out infinite;
}

@keyframes wave-motion {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(5px);
  }
}

/* Cloud animations */
.cloud-1 {
  animation: cloud-drift-1 8s ease-in-out infinite;
}

.cloud-2 {
  animation: cloud-drift-2 10s ease-in-out infinite;
}

@keyframes cloud-drift-1 {
  0%, 100% {
    transform: translateX(0px);
    opacity: 0.6;
  }
  50% {
    transform: translateX(10px);
    opacity: 0.3;
  }
}

@keyframes cloud-drift-2 {
  0%, 100% {
    transform: translateX(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateX(-8px);
    opacity: 0.2;
  }
}

/* Additional lighthouse effects */
.lighthouse {
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

/* Responsive lighthouse sizing */
@media (max-width: 768px) {
  .lighthouse-container svg {
    width: 250px;
    height: 350px;
  }
}

@media (max-width: 640px) {
  .lighthouse-container svg {
    width: 200px;
    height: 300px;
  }
}
